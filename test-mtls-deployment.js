#!/usr/bin/env node

/**
 * mTLS Deployment Validation Script
 *
 * This script validates that the current development environment deployment
 * properly implements mTLS between workers and API, and verifies certificate configuration.
 */

const fs = require("fs");
const path = require("path");
const https = require("https");
const { URL } = require("url");

// Configuration
const CONFIG = {
  // Certificate paths (server-only mTLS)
  certPaths: {
    ca: "workspace/resources/mtls/ca/ca-cert.pem",
    serverCert: "workspace/resources/mtls/server/server-cert.pem",
    serverKey: "workspace/resources/mtls/server/server-key.pem",
  },

  // Test endpoints
  endpoints: {
    localApi: "https://localhost:3000/health",
    devApi: "https://dev-api.divinci.ai/health",
    stagingApi: "https://staging-api.divinci.ai/health",
  },

  // Worker services to test
  workers: ["audio-speaker-diarization@pyannote", "open-parse"],

  // Test timeout
  timeout: 10000,
};

/**
 * Main test execution
 */
async function main() {
  console.log("🔐 mTLS Deployment Validation Script");
  console.log("=====================================\n");

  const results = {
    certificates: {},
    workers: {},
    connectivity: {},
    summary: {
      totalTests: 0,
      passed: 0,
      failed: 0,
      warnings: 0,
    },
  };

  try {
    // Test 1: Certificate validation
    console.log("📋 Step 1: Validating mTLS Certificates");
    console.log("----------------------------------------");
    const certResults = await validateCertificates();
    results.certificates = certResults;
    updateSummary(results.summary, certResults);

    // Test 2: Worker configuration validation
    console.log("\n📋 Step 2: Validating Worker mTLS Configuration");
    console.log("-----------------------------------------------");
    const workerResults = await validateWorkerConfiguration();
    results.workers = workerResults;
    updateSummary(results.summary, workerResults);

    // Test 3: Service connectivity testing
    console.log("\n📋 Step 3: Testing mTLS Connectivity");
    console.log("------------------------------------");
    const connectivityResults = await testConnectivity();
    results.connectivity = connectivityResults;
    updateSummary(results.summary, connectivityResults);

    // Test 4: ServiceFetch validation
    console.log("\n📋 Step 4: Testing ServiceFetch mTLS Integration");
    console.log("------------------------------------------------");
    const serviceFetchResults = await testServiceFetch();
    results.serviceFetch = serviceFetchResults;
    updateSummary(results.summary, serviceFetchResults);

    // Generate final report
    console.log("\n📊 Final Test Report");
    console.log("====================");
    generateReport(results);
  } catch (error) {
    console.error("❌ Test execution failed:", error.message);
    process.exit(1);
  }
}

/**
 * Validate mTLS certificates
 */
async function validateCertificates() {
  const results = {};

  for (const [certType, certPath] of Object.entries(CONFIG.certPaths)) {
    const fullPath = path.resolve(certPath);

    console.log(`  🔍 Checking ${certType} certificate: ${certPath}`);

    const result = {
      path: fullPath,
      exists: false,
      size: 0,
      format: "unknown",
      valid: false,
      errors: [],
      warnings: [],
    };

    try {
      if (fs.existsSync(fullPath)) {
        result.exists = true;
        const stats = fs.statSync(fullPath);
        result.size = stats.size;

        if (result.size > 0) {
          const content = fs.readFileSync(fullPath, "utf8");

          // Validate certificate format
          if (
            content.includes("-----BEGIN CERTIFICATE-----") &&
            content.includes("-----END CERTIFICATE-----")
          ) {
            result.format = "PEM Certificate";
            result.valid = true;
          } else if (
            content.includes("-----BEGIN PRIVATE KEY-----") ||
            content.includes("-----BEGIN RSA PRIVATE KEY-----")
          ) {
            result.format = "PEM Private Key";
            result.valid = true;
          } else {
            result.format = "Unknown";
            result.errors.push("Invalid certificate format");
          }

          if (content.length < 100) {
            result.warnings.push("Certificate content seems unusually short");
          }
        } else {
          result.errors.push("Certificate file is empty");
        }
      } else {
        result.errors.push("Certificate file does not exist");
      }

      console.log(
        `    ${result.valid ? "✅" : "❌"} ${certType}: ${
          result.valid ? "Valid" : "Invalid"
        }`
      );
      if (result.errors.length > 0) {
        result.errors.forEach((error) => console.log(`      ❌ ${error}`));
      }
      if (result.warnings.length > 0) {
        result.warnings.forEach((warning) =>
          console.log(`      ⚠️  ${warning}`)
        );
      }
    } catch (error) {
      result.errors.push(`Error reading certificate: ${error.message}`);
      console.log(`    ❌ ${certType}: Error - ${error.message}`);
    }

    results[certType] = result;
  }

  return results;
}

/**
 * Validate worker mTLS configuration
 */
async function validateWorkerConfiguration() {
  const results = {};

  for (const workerName of CONFIG.workers) {
    console.log(`  🔍 Checking ${workerName} worker configuration`);

    const result = {
      name: workerName,
      hasUtilsFile: false,
      utilsPath: "",
      hasMTLSFunctions: false,
      missingFunctions: [],
      valid: false,
    };

    try {
      const utilsPath = path.resolve(
        `workspace/workers/${workerName}/mtls_utils.py`
      );
      result.utilsPath = utilsPath;

      if (fs.existsSync(utilsPath)) {
        result.hasUtilsFile = true;

        const utilsContent = fs.readFileSync(utilsPath, "utf8");

        const requiredFunctions = [
          "is_mtls_enabled",
          "get_mtls_config",
          "create_mtls_session",
          "configure_flask_mtls",
        ];

        const missingFunctions = requiredFunctions.filter(
          (func) => !utilsContent.includes(`def ${func}`)
        );

        result.missingFunctions = missingFunctions;
        result.hasMTLSFunctions = missingFunctions.length === 0;
        result.valid = result.hasUtilsFile && result.hasMTLSFunctions;

        console.log(
          `    ${result.valid ? "✅" : "❌"} ${workerName}: ${
            result.valid ? "Valid" : "Invalid"
          }`
        );

        if (missingFunctions.length > 0) {
          console.log(
            `      ❌ Missing functions: ${missingFunctions.join(", ")}`
          );
        }
      } else {
        console.log(`    ❌ ${workerName}: mTLS utils file not found`);
      }
    } catch (error) {
      console.log(`    ❌ ${workerName}: Error - ${error.message}`);
    }

    results[workerName] = result;
  }

  return results;
}

/**
 * Test mTLS connectivity
 */
async function testConnectivity() {
  const results = {};

  for (const [endpointName, endpointUrl] of Object.entries(CONFIG.endpoints)) {
    console.log(`  🔍 Testing connectivity to ${endpointName}: ${endpointUrl}`);

    const result = {
      endpoint: endpointUrl,
      reachable: false,
      responseTime: 0,
      statusCode: 0,
      error: null,
      usedMTLS: false,
    };

    const startTime = Date.now();

    try {
      // Determine if this should use mTLS
      const url = new URL(endpointUrl);
      result.usedMTLS = isInternalEndpoint(url.hostname);

      const response = await makeRequest(endpointUrl, result.usedMTLS);

      result.reachable = true;
      result.responseTime = Date.now() - startTime;
      result.statusCode = response.statusCode;

      console.log(
        `    ✅ ${endpointName}: Reachable (${result.statusCode}) in ${result.responseTime}ms`
      );
    } catch (error) {
      result.responseTime = Date.now() - startTime;
      result.error = error.message;

      // Check if it's a certificate-related error (indicates mTLS was attempted)
      const isCertError =
        error.message.toLowerCase().includes("certificate") ||
        error.message.toLowerCase().includes("ssl") ||
        error.message.toLowerCase().includes("tls");

      if (result.usedMTLS && isCertError) {
        console.log(
          `    ⚠️  ${endpointName}: Certificate error (mTLS attempted) - ${error.message}`
        );
      } else {
        console.log(`    ❌ ${endpointName}: Failed - ${error.message}`);
      }
    }

    results[endpointName] = result;
  }

  return results;
}

/**
 * Test ServiceFetch mTLS integration
 */
async function testServiceFetch() {
  console.log("  🔍 Testing ServiceFetch mTLS integration");

  const result = {
    moduleLoaded: false,
    internalCallTest: { success: false, error: null },
    externalCallTest: { success: false, error: null },
    valid: false,
  };

  try {
    // Try to load serviceFetch module
    const {
      serviceFetch,
    } = require("./workspace/resources/server-utils/dist/http-request/service-fetch");
    result.moduleLoaded = true;
    console.log("    ✅ ServiceFetch module loaded successfully");

    // Test internal service call (should use mTLS)
    const internalUrl = `https://localhost:3000/health`;
    console.log(`    🔐 Testing internal service call: ${internalUrl}`);

    try {
      const response = await serviceFetch(internalUrl);
      const data = await response.json();

      result.internalCallTest.success = true;
      console.log(`    ✅ Internal service call successful`);
      console.log(`    📊 Response status: ${response.status}`);
      console.log(`    📊 Response data:`, data);
    } catch (error) {
      result.internalCallTest.error = error.message;
      console.log(
        `    ⚠️  Internal service call failed (expected in test environment): ${error.message}`
      );
      // This is expected since we're testing with self-signed certificates
    }

    // Test external service call (should use regular HTTPS)
    const externalUrl = "https://httpbin.org/json";
    console.log(`    🌐 Testing external service call: ${externalUrl}`);

    try {
      const response = await serviceFetch(externalUrl);
      const data = await response.json();

      result.externalCallTest.success = true;
      console.log(`    ✅ External service call successful`);
      console.log(`    📊 Response status: ${response.status}`);
      console.log(`    📊 Response has data: ${!!data}`);
    } catch (error) {
      result.externalCallTest.error = error.message;
      console.log(`    ⚠️  External service call failed: ${error.message}`);
      // This might fail due to network issues, but that's okay for testing
    }

    result.valid = result.moduleLoaded;
  } catch (error) {
    result.error = error.message;
    console.log(`    ❌ ServiceFetch test failed: ${error.message}`);
  }

  return result;
}

/**
 * Make HTTP request with optional mTLS
 */
function makeRequest(url, useMTLS = false) {
  return new Promise((resolve, reject) => {
    const options = {
      timeout: CONFIG.timeout,
      rejectUnauthorized: false, // For testing with self-signed certificates
    };

    if (useMTLS) {
      // Add mTLS configuration if CA certificate exists (server-only mTLS)
      try {
        const caPath = path.resolve(CONFIG.certPaths.ca);

        if (fs.existsSync(caPath)) {
          options.ca = fs.readFileSync(caPath);
          // For server-only mTLS, we only need the CA cert to verify the server
        }
      } catch (error) {
        // Certificate loading failed, continue without mTLS
      }
    }

    const req = https.get(url, options, (res) => {
      resolve(res);
    });

    req.on("error", (error) => {
      reject(error);
    });

    req.on("timeout", () => {
      req.destroy();
      reject(new Error("Request timeout"));
    });
  });
}

/**
 * Check if endpoint is internal (should use mTLS)
 */
function isInternalEndpoint(hostname) {
  const internalHosts = [
    "localhost",
    "127.0.0.1",
    "0.0.0.0",
    "api.divinci.ai",
    "staging-api.divinci.ai",
    "dev-api.divinci.ai",
  ];

  return (
    internalHosts.some((host) => hostname.includes(host)) ||
    hostname.endsWith(".internal") ||
    hostname.endsWith(".local")
  );
}

/**
 * Update summary statistics
 */
function updateSummary(summary, results) {
  if (Array.isArray(results)) {
    results.forEach((result) => {
      summary.totalTests++;
      if (result.valid || result.success) {
        summary.passed++;
      } else {
        summary.failed++;
      }
      if (result.warnings && result.warnings.length > 0) {
        summary.warnings += result.warnings.length;
      }
    });
  } else if (typeof results === "object") {
    Object.values(results).forEach((result) => {
      summary.totalTests++;
      if (result.valid || result.success) {
        summary.passed++;
      } else {
        summary.failed++;
      }
      if (result.warnings && result.warnings.length > 0) {
        summary.warnings += result.warnings.length;
      }
    });
  }
}

/**
 * Generate final test report
 */
function generateReport(results) {
  const { summary } = results;

  console.log(`📊 Total Tests: ${summary.totalTests}`);
  console.log(`✅ Passed: ${summary.passed}`);
  console.log(`❌ Failed: ${summary.failed}`);
  console.log(`⚠️  Warnings: ${summary.warnings}`);

  const successRate =
    summary.totalTests > 0
      ? ((summary.passed / summary.totalTests) * 100).toFixed(1)
      : 0;
  console.log(`📈 Success Rate: ${successRate}%`);

  if (summary.failed === 0) {
    console.log(
      "\n🎉 All mTLS tests passed! Deployment appears to be properly configured."
    );
  } else if (summary.passed > summary.failed) {
    console.log(
      "\n⚠️  Some mTLS tests failed, but basic functionality appears to work."
    );
  } else {
    console.log(
      "\n❌ Multiple mTLS tests failed. Please review the configuration."
    );
  }

  // Save detailed report to file
  const reportPath = "mtls-test-report.json";
  fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));
  console.log(`\n📄 Detailed report saved to: ${reportPath}`);
}

// Run the tests
if (require.main === module) {
  main().catch((error) => {
    console.error("❌ Test execution failed:", error);
    process.exit(1);
  });
}

module.exports = {
  main,
  validateCertificates,
  validateWorkerConfiguration,
  testConnectivity,
  testServiceFetch,
};
